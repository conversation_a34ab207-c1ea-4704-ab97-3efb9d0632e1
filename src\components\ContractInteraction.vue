<template>
  <BaseLayout layout-class="contract-interaction">
    <!-- 左侧配置面板 -->
    <template #left>
      <BaseCard title="网络配置" variant="config">
        <!-- 网络选择器 -->
        <NetworkSelector 
          v-model="rpcUrl"
          :default-network="selectedNetwork"
          @network-change="handleNetworkChange"
          @rpc-change="handleRpcChange"
        />
        
        <!-- RPC测试 -->
        <RpcTester 
          :rpc-url="rpcUrl"
          @connection-status="handleConnectionStatus"
          @gas-price-update="handleGasPriceUpdate"
        />
      </BaseCard>

      <BaseCard title="交互配置" variant="config">
        <!-- 模式选择 -->
        <div class="mode-selector">
          <label class="mode-option">
            <input
              type="radio"
              v-model="interactionMode"
              value="contract"
              class="mode-radio"
            />
            <span class="mode-text">合约交互</span>
          </label>
          <label class="mode-option">
            <input
              type="radio"
              v-model="interactionMode"
              value="eth-collection"
              class="mode-radio"
            />
            <span class="mode-text">ETH归集</span>
          </label>
          <label class="mode-option">
            <input
              type="radio"
              v-model="interactionMode"
              value="token-collection"
              class="mode-radio"
            />
            <span class="mode-text">代币归集</span>
          </label>
        </div>
        <FormInput
          v-model="targetAddress"
          :label="getTargetAddressLabel()"
          placeholder="0x..."
          wide
        />

        <!-- 代币归集模式下的代币配置 -->
        <template v-if="interactionMode === 'token-collection'">
          <FormInput
            v-model="tokenContractAddress"
            label="ERC20代币合约地址"
            placeholder="0x..."
            wide
          />

          <FormInput
            v-model="collectionAmount"
            label="归集数量"
            type="number"
            :step="0.000000001"
            :min="0"
            :disabled="useMaxAmount"
            placeholder="输入归集数量"
          >
            <template #addon>
              <div class="collection-mode">
                <label class="checkbox-label">
                  <input
                    type="checkbox"
                    v-model="useMaxAmount"
                    class="checkbox-input"
                  />
                  <span class="checkbox-text">最大余额归集</span>
                </label>
                <div v-if="useMaxAmount" class="collection-tip-inline">
                  💡 自动归集所有可用代币余额
                </div>
              </div>
            </template>
          </FormInput>

          <!-- 代币信息显示 -->
          <div v-if="tokenInfo" class="token-info">
            <div class="info-row compact">
              <span class="label">代币名称:</span>
              <span class="value">{{ tokenInfo.name }} ({{ tokenInfo.symbol }})</span>
            </div>
          </div>

          <ActionButtonGroup>
            <BaseButton
              @click="refreshTokenInfo"
              :disabled="!tokenContractAddress || !web3"
              variant="secondary"
            >
              刷新代币信息
            </BaseButton>

            <BaseButton
              @click="refreshTokenBalances"
              :disabled="!tokenContractAddress || !tokenInfo || walletData.length === 0 || !web3"
              variant="secondary"
            >
              刷新代币余额
            </BaseButton>
          </ActionButtonGroup>
        </template>

        <!-- 合约交互模式下的配置 -->
        <template v-if="interactionMode === 'contract'">
          <FormInput
            v-model="contractData"
            label="合约数据"
            placeholder="0x..."
            wide
          />

          <FormInput
            v-model="transferValue"
            label="转账金额 (ETH)"
            type="number"
            :step="0.000000001"
            :min="0"
          />
        </template>

        <!-- ETH归集模式下的提示 -->
        <template v-if="interactionMode === 'eth-collection'">
          <div class="collection-tip">
            💡 ETH归集模式：自动计算最大可转金额 (余额 - Gas费用)
          </div>
        </template>
        
        <FormInput
          v-model="gasLimit"
          label="Gas限制"
          type="number"
          :min="21000"
          :disabled="interactionMode === 'eth-collection'"
        />

        <FormInput
          v-model="loopCount"
          label="循环次数"
          type="number"
          :min="1"
          :max="100"
          placeholder="1"
          :disabled="isCollectionMode"
        />

        <div class="loop-mode-info">
          <div class="info-text" v-if="interactionMode === 'contract'">
            💡 循环模式：{{ loopCount }} 轮循环，每轮所有钱包各执行一次
          </div>
          <div class="info-text" v-else-if="interactionMode === 'eth-collection'">
            💡 ETH归集模式：每个钱包执行一次，转移全部ETH余额
          </div>
          <div class="info-text" v-else-if="interactionMode === 'token-collection'">
            💡 代币归集模式：每个钱包执行一次，转移{{ useMaxAmount ? '全部' : '指定数量' }}代币
          </div>
        </div>
      </BaseCard>

      <BaseCard title="Gas价格设置" variant="config">
        <FormInput 
          v-model="maxGasPrice"
          label="最大Gas价格 (Gwei)"
          type="number"
          :step="0.001"
          :min="0"
        />
        
        <FormInput 
          v-model="additionalGas"
          label="额外Gas (Gwei)"
          type="number"
          :step="0.001"
          :min="0"
        />
      </BaseCard>
    </template>

    <!-- 中间钱包面板 -->
    <template #middle>
      <BaseCard title="钱包数据" variant="config">
        <!-- 文件格式帮助 -->
        <FormatHelp title="📋 支持的文件格式 (address-w.txt)">
          <FormatItem 
            title="格式1: 地址 + 私钥 (空格分隔)"
            :examples="[
              '0x1234...abcd 0xabcd1234...5678',
              '0x5678...efgh 0xefgh5678...9abc'
            ]"
          />
          <FormatItem 
            title="格式2: 仅私钥 (每行一个)"
            :examples="[
              '0xabcd1234...5678',
              '0xefgh5678...9abc'
            ]"
            description="地址将自动从私钥推导生成"
          />
        </FormatHelp>
        
        <!-- 数据输入 -->
        <DataInput 
          accepted-types=".txt"
          file-description="支持 .txt 文件格式 (地址 私钥 或 每行一个私钥)"
          text-input-label="直接输入钱包数据"
          text-placeholder="请输入钱包数据，每行一条记录：&#10;格式1: 地址 私钥 (空格分隔)&#10;格式2: 仅私钥 (每行一个)&#10;&#10;示例:&#10;0x1234...abcd 0xabcd1234...5678&#10;0x5678...efgh 0xefgh5678...9abc"
          text-input-description="💡 支持两种格式：地址+私钥(空格分隔) 或 仅私钥(自动推导地址)"
          @data-parsed="handleDataParsed"
          @error="handleFileError"
        />
        
        <!-- 钱包预览 -->
        <WalletPreview
          :wallets="walletData"
          :show-token-balance="interactionMode === 'token-collection'"
          :token-symbol="tokenInfo?.symbol || 'TOKEN'"
        />

        <ActionButtonGroup>
          <template #buttons>
            <BaseButton
              variant="success"
              :disabled="isRunning || walletData.length === 0"
              :loading="isRunning"
              loading-text="执行中..."
              @click="startInteraction"
            >
              🚀 开始执行
            </BaseButton>

            <BaseButton
              variant="danger"
              :disabled="!isRunning"
              @click="stopInteraction"
            >
              ⏹️ 停止执行
            </BaseButton>

            <BaseButton
              variant="secondary"
              :disabled="walletData.length === 0"
              @click="refreshWalletBalances"
            >
              🔄 刷新余额
            </BaseButton>

            <BaseButton
              variant="danger"
              @click="handleClearData"
            >
              🗑️ 清除保存的数据
            </BaseButton>
          </template>
        </ActionButtonGroup>
      </BaseCard>
    </template>

    <!-- 右侧状态面板 -->
    <template #right>
      <!-- 执行状态 -->
      <BaseCard v-if="isRunning" title="📊 执行状态" variant="status">
        <StatusGrid :items="executionStatusItems" class="execution-status-grid" />
        <ProgressBar 
          :current="executedCount" 
          :total="walletData.length" 
          :message="statusMessage"
        />
      </BaseCard>

      <!-- 交易记录 -->
      <TransactionResults 
        :results="transactionResults"
        @clear="clearTransactionResults"
      />
    </template>
  </BaseLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// 组件导入
import BaseLayout from '@/components/layout/BaseLayout.vue'
import BaseCard from '@/components/common/BaseCard.vue'
import BaseButton from '@/components/common/BaseButton.vue'
import FormInput from '@/components/common/FormInput.vue'
import ActionButtonGroup from '@/components/common/ActionButtonGroup.vue'
import FormatHelp from '@/components/common/FormatHelp.vue'
import FormatItem from '@/components/common/FormatItem.vue'
import TransactionResults from '@/components/common/TransactionResults.vue'
import NetworkSelector from '@/components/network/NetworkSelector.vue'
import RpcTester from '@/components/network/RpcTester.vue'
import DataInput from '@/components/common/DataInput.vue'
import WalletPreview from '@/components/wallet/WalletPreview.vue'
import StatusGrid from '@/components/common/StatusGrid.vue'
import ProgressBar from '@/components/common/ProgressBar.vue'
import AddressDisplay from '@/components/common/AddressDisplay.vue'

// Composables导入
import { useStorage } from '@/composables/useStorage'
import { useWeb3 } from '@/composables/useWeb3'
import { useFileHandler } from '@/composables/useFileHandler'

// 工具函数导入
import { STORAGE_KEYS, DEFAULT_VALUES, UI_CONFIG } from '@/utils/constants'
import { formatTxHash, formatGasPrice } from '@/utils/formatters'
import { sleep, getErrorMessage } from '@/utils/helpers'

// 响应式数据
const selectedNetwork = ref('以太坊测试网')
const rpcUrl = ref('https://eth-sepolia.g.alchemy.com/v2/********************************')
const interactionMode = ref('contract') // 'contract', 'eth-collection', 'token-collection'
const targetAddress = ref('')
const contractData = ref('')
const transferValue = ref(0)
const gasLimit = ref(21000)
const maxGasPrice = ref(0) // 将在初始化时设置为实时Gas价格
const additionalGas = ref(0)
const loopCount = ref(1)

// 代币归集相关数据
const tokenContractAddress = ref('')
const collectionAmount = ref(0)
const useMaxAmount = ref(true)
const tokenInfo = ref(null)

const walletData = ref([])
const isRunning = ref(false)
const executedCount = ref(0)
const successCount = ref(0)
const failureCount = ref(0)
const statusMessage = ref('')
const transactionResults = ref([])
const currentLoop = ref(0)
const currentWallet = ref(0)
const totalTransactions = ref(0)

let stopFlag = false

// 使用composables
const {
  web3,
  currentGasPrice,
  initWeb3,
  getCurrentGasPrice,
  loadWalletBalances,
  getTokenInfo,
  getTokenBalance,
  formatTokenBalance,
  parseTokenAmount,
  loadWalletTokenBalances,
  executeTokenTransfer
} = useWeb3()

const { parseWalletFile } = useFileHandler()

// 监听交互模式变化
watch(interactionMode, (newValue) => {
  if (newValue === 'eth-collection' || newValue === 'token-collection') {
    // 切换到归集模式时，清空合约数据并固定Gas限制
    contractData.value = ''
    gasLimit.value = newValue === 'token-collection' ? 210000 : 21000
    // 同时清空用户输入的转账金额
    transferValue.value = 0
    // 归集模式下设置循环次数为1
    loopCount.value = 1
  }
})

// 监听最大余额模式变化
watch(useMaxAmount, (newValue) => {
  if (newValue) {
    collectionAmount.value = 0
  }
})

// 监听代币合约地址变化，自动获取代币信息和余额
watch(tokenContractAddress, async (newAddress) => {
  if (newAddress && web3.value && interactionMode.value === 'token-collection') {
    // 自动获取代币信息
    await refreshTokenInfo()
  }
})

const { clearStoredData } = useStorage(
  STORAGE_KEYS.CONTRACT_INTERACTION,
  {
    selectedNetwork,
    rpcUrl,
    interactionMode,
    targetAddress,
    contractData,
    transferValue,
    gasLimit,
    maxGasPrice,
    additionalGas,
    walletData,
    transactionResults,
    loopCount,
    tokenContractAddress,
    collectionAmount,
    useMaxAmount,
    tokenInfo
  },
  DEFAULT_VALUES.CONTRACT_INTERACTION
)

// 计算属性
const isCollectionMode = computed(() => {
  return interactionMode.value === 'eth-collection' || interactionMode.value === 'token-collection'
})

const executionStatusItems = computed(() => [
  {
    label: '当前Gas价格',
    value: formatGasPrice(currentGasPrice.value),
    status: currentGasPrice.value > maxGasPrice.value ? 'gas-high' : 'success'
  },
  {
    label: '钱包进度',
    value: `${currentWallet.value}/${walletData.value.length}`,
    status: 'success'
  },
  {
    label: '循环进度',
    value: `${currentLoop.value}/${loopCount.value}`,
    status: 'success'
  },
  {
    label: '总进度',
    value: `${executedCount.value}/${totalTransactions.value}`,
    status: 'success'
  },
  {
    label: '成功交易',
    value: successCount.value.toString(),
    status: 'success'
  },
  {
    label: '失败交易',
    value: failureCount.value.toString(),
    status: failureCount.value > 0 ? 'error' : 'success'
  }
])

const recentTransactionResults = computed(() => {
  return transactionResults.value.slice(-UI_CONFIG.MAX_RECENT_TRANSACTIONS)
})

// 计算总交易数
function calculateTotalTransactions() {
  // 归集模式下每个钱包只执行一次
  if (isCollectionMode.value) {
    return walletData.value.length
  }
  // 普通模式下按循环次数计算
  return walletData.value.length * loopCount.value
}

// 获取目标地址标签
function getTargetAddressLabel() {
  switch (interactionMode.value) {
    case 'eth-collection':
      return 'ETH归集目标地址'
    case 'token-collection':
      return '代币归集目标地址'
    default:
      return '目标地址'
  }
}

// 网络配置变化时同步到批量分发页面
function syncNetworkConfigToBatchDisperse() {
  // 由于批量分发页面从ContractInteraction读取配置，我们只需要确保存储已更新
  console.log('网络配置已更新到本地存储，其他页面将自动同步')
}

// 网络变化处理
async function handleNetworkChange(data) {
  selectedNetwork.value = data.network
  rpcUrl.value = data.url
  syncNetworkConfigToBatchDisperse() // 同步到批量分发
  // 切换RPC/网络时重置额外Gas为0，避免沿用旧配置
  additionalGas.value = 0
  
  // 网络切换后总是重新初始化Web3并刷新所有数据
  console.log('网络切换到:', data.network, data.url)
  initWeb3(data.url)
  
  // 延迟一下等待连接稳定后刷新所有数据
  setTimeout(async () => {
    const gasPrice = await getCurrentGasPrice()
    if (gasPrice > 0) {
      currentGasPrice.value = gasPrice
      const rounded = Number(gasPrice.toFixed(4))
      // 网络切换时也更新最大Gas，保证下拉切换网络生效
      maxGasPrice.value = rounded
      console.log('网络切换后更新最大Gas为实时价格:', rounded, 'Gwei')
    }
    // 如果已经加载了钱包，刷新它们的余额
    if (walletData.value.length > 0) {
      console.log('网络切换后刷新已加载钱包的余额...')
      const updatedWallets = await loadWalletBalances(walletData.value)
      walletData.value = updatedWallets

      // 如果是代币归集模式且已设置代币信息，同时刷新代币余额
      if (interactionMode.value === 'token-collection' && tokenInfo.value) {
        console.log('刷新钱包代币余额...')
        const walletsWithTokenBalance = await loadWalletTokenBalances(
          updatedWallets,
          tokenContractAddress.value,
          tokenInfo.value.decimals
        )
        walletData.value = walletsWithTokenBalance
      }

      console.log('钱包余额刷新完成')
    }
  }, 500)
}

async function handleRpcChange(url) {
  rpcUrl.value = url
  syncNetworkConfigToBatchDisperse() // 同步到批量分发
  // 切换RPC时重置额外Gas为0
  additionalGas.value = 0
  
  // RPC切换后总是重新初始化Web3并刷新所有数据
  console.log('RPC切换到:', url)
  initWeb3(url)
  
  // 延迟一下等待连接稳定后刷新所有数据
  setTimeout(async () => {
    const gasPrice = await getCurrentGasPrice()
    if (gasPrice > 0) {
      currentGasPrice.value = gasPrice
      const rounded = Number(gasPrice.toFixed(4))
      // RPC切换时总是更新最大Gas
      maxGasPrice.value = rounded
      console.log('RPC切换后更新最大Gas为实时价格:', rounded, 'Gwei')
    }
    
    // 如果已经加载了钱包，刷新它们的余额
    if (walletData.value.length > 0) {
      console.log('RPC切换后刷新已加载钱包的余额...')
      const updatedWallets = await loadWalletBalances(walletData.value)
      walletData.value = updatedWallets

      // 如果是代币归集模式且已设置代币信息，同时刷新代币余额
      if (interactionMode.value === 'token-collection' && tokenInfo.value) {
        console.log('刷新钱包代币余额...')
        const walletsWithTokenBalance = await loadWalletTokenBalances(
          updatedWallets,
          tokenContractAddress.value,
          tokenInfo.value.decimals
        )
        walletData.value = walletsWithTokenBalance
      }

      console.log('钱包余额刷新完成')
    }
  }, 500)
}

function handleConnectionStatus(success) {
  if (success) {
    console.log('RPC连接成功')
  }
}

function handleGasPriceUpdate(gasPrice) {
  currentGasPrice.value = gasPrice
}

// 刷新代币信息
async function refreshTokenInfo() {
  if (!tokenContractAddress.value || !web3.value) {
    return
  }

  try {
    console.log('获取代币信息:', tokenContractAddress.value)
    const info = await getTokenInfo(tokenContractAddress.value)

    if (!info) {
      return
    }

    tokenInfo.value = info
    console.log('代币信息获取成功:', info)

    // 如果已经加载了钱包，自动刷新代币余额
    if (walletData.value.length > 0) {
      console.log('自动刷新钱包代币余额...')
      const updatedWallets = await loadWalletTokenBalances(
        walletData.value,
        tokenContractAddress.value,
        info.decimals
      )
      walletData.value = updatedWallets
      console.log('代币余额刷新完成')
    }

    // 移除弹窗提醒，静默获取代币信息
  } catch (error) {
    console.error('获取代币信息失败:', error)
    // 静默处理错误，不显示弹窗
  }
}

// 刷新代币余额
async function refreshTokenBalances() {
  if (!tokenContractAddress.value || !tokenInfo.value || walletData.value.length === 0 || !web3.value) {
    alert('请确保已设置代币信息且已加载钱包数据')
    return
  }

  try {
    console.log('刷新钱包代币余额...')
    const updatedWallets = await loadWalletTokenBalances(
      walletData.value,
      tokenContractAddress.value,
      tokenInfo.value.decimals
    )
    walletData.value = updatedWallets
    console.log('代币余额刷新完成')
  } catch (error) {
    console.error('刷新代币余额失败:', error)
    alert('刷新代币余额失败: ' + getErrorMessage(error))
  }
}

// 刷新钱包余额（包括ETH和代币余额）
async function refreshWalletBalances() {
  if (walletData.value.length === 0 || !web3.value) {
    alert('请先加载钱包数据并确保网络连接正常')
    return
  }

  try {
    console.log('刷新钱包余额...')

    // 刷新ETH余额
    const updatedWallets = await loadWalletBalances(walletData.value)
    walletData.value = updatedWallets

    // 如果是代币归集模式且已设置代币信息，同时刷新代币余额
    if (interactionMode.value === 'token-collection' && tokenInfo.value && tokenContractAddress.value) {
      console.log('同时刷新代币余额...')
      const walletsWithTokenBalance = await loadWalletTokenBalances(
        updatedWallets,
        tokenContractAddress.value,
        tokenInfo.value.decimals
      )
      walletData.value = walletsWithTokenBalance
    }

    console.log('钱包余额刷新完成')
  } catch (error) {
    console.error('刷新钱包余额失败:', error)
    alert('刷新钱包余额失败: ' + getErrorMessage(error))
  }
}

// 数据处理
async function handleDataParsed(file, inputType) {
  try {
    // 确保使用最新的Web3实例
    console.log(`处理钱包数据 (${inputType === 'text' ? '文本输入' : '文件上传'})，使用RPC端点:`, rpcUrl.value)
    initWeb3(rpcUrl.value)
    
    if (!web3.value) {
      alert('Web3初始化失败')
      return
    }
    
    const wallets = await parseWalletFile(file, web3.value)
    
    if (wallets.length === 0) {
      alert('未能解析到有效的钱包数据，请检查文件格式')
      return
    }
    
    walletData.value = wallets
    alert(`成功加载 ${wallets.length} 个钱包，正在获取余额信息...`)
    
    // 获取所有钱包的余额（使用最新的Web3实例）
    const walletsWithBalance = await loadWalletBalances(wallets)
    walletData.value = walletsWithBalance

    // 如果是代币归集模式，尝试获取代币信息和余额
    if (interactionMode.value === 'token-collection') {
      // 如果已经输入了代币合约地址但还没有代币信息，先获取代币信息
      if (tokenContractAddress.value && !tokenInfo.value) {
        console.log('检测到代币合约地址，自动获取代币信息...')
        await refreshTokenInfo()
      }

      // 如果已有代币信息，获取代币余额
      if (tokenInfo.value && tokenContractAddress.value) {
        console.log('获取钱包代币余额...')
        const walletsWithTokenBalance = await loadWalletTokenBalances(
          walletsWithBalance,
          tokenContractAddress.value,
          tokenInfo.value.decimals
        )
        walletData.value = walletsWithTokenBalance
      }
    }
  } catch (error) {
    console.error('文件处理失败:', error)
    alert('文件处理失败: ' + getErrorMessage(error))
  }
}

function handleFileError(error) {
  console.error('文件上传错误:', error)
}

// 清除数据
async function handleClearData() {
  try {
    await clearStoredData()
    walletData.value = []
    transactionResults.value = []
    tokenInfo.value = null
  } catch (error) {
    // 用户取消操作
  }
}

// 清除交易记录
function clearTransactionResults() {
  if (confirm('确定要清除所有交易记录吗？')) {
    transactionResults.value = []
  }
}

// 执行单个钱包的交易（带重试机制）
async function executeTransaction(wallet, retryCount = 0) {
  const maxRetries = 2

  try {
    // 确保使用最新的Web3实例
    if (!web3.value) {
      console.log('执行交易时重新初始化Web3，使用RPC:', rpcUrl.value)
      initWeb3(rpcUrl.value)
    }

    if (!web3.value) {
      throw new Error('Web3初始化失败')
    }

    console.log('执行交易，使用RPC端点:', rpcUrl.value)

    // 根据交互模式执行不同的逻辑
    if (interactionMode.value === 'token-collection') {
      return await executeTokenCollection(wallet, retryCount)
    } else {
      return await executeEthTransaction(wallet, retryCount)
    }
  } catch (error) {
    const errorMessage = error.message.toLowerCase()

    // 检查是否是nonce相关错误，如果是且还有重试次数，则重试
    if (errorMessage.includes('nonce') && retryCount < maxRetries) {
      console.log(`Nonce错误，等待2秒后重试 (${retryCount + 1}/${maxRetries}): ${wallet.address}`)
      await sleep(2)
      return await executeTransaction(wallet, retryCount + 1)
    }

    return {
      success: false,
      error: getErrorMessage(error),
      address: wallet.address
    }
  }
}

// 执行代币归集
async function executeTokenCollection(wallet, retryCount = 0) {
  try {
    console.log('执行代币归集，钱包地址:', wallet.address)

    // 检查ETH余额（用于支付gas费）
    const ethBalance = await web3.value.eth.getBalance(wallet.address)
    if (ethBalance === '0') {
      throw new Error('钱包ETH余额不足，无法支付Gas费用')
    }

    // 检查代币余额
    const tokenBalance = await getTokenBalance(tokenContractAddress.value, wallet.address)
    if (tokenBalance === '0') {
      throw new Error('钱包代币余额为0')
    }

    // 确定转账数量
    let transferAmount
    if (useMaxAmount.value) {
      transferAmount = tokenBalance // 使用全部余额
    } else {
      if (!collectionAmount.value || collectionAmount.value <= 0) {
        throw new Error('请输入有效的归集数量')
      }
      transferAmount = parseTokenAmount(collectionAmount.value.toString(), tokenInfo.value.decimals)

      // 检查余额是否足够
      if (BigInt(transferAmount) > BigInt(tokenBalance)) {
        throw new Error('钱包代币余额不足')
      }
    }

    // 执行代币转账
    const result = await executeTokenTransfer(
      wallet,
      tokenContractAddress.value,
      targetAddress.value,
      transferAmount,
      gasLimit.value,
      maxGasPrice.value,
      additionalGas.value
    )

    return result
  } catch (error) {
    return {
      success: false,
      error: getErrorMessage(error),
      address: wallet.address
    }
  }
}

// 执行ETH交易
async function executeEthTransaction(wallet, retryCount = 0) {
  try {
    // 检查余额
    const balance = await web3.value.eth.getBalance(wallet.address)
    if (balance === '0') {
      throw new Error('钱包余额不足')
    }

    let value
    const gas = gasLimit.value || 21000

    // 获取当前gas价格并添加额外gas
    const currentGasPriceBN = await web3.value.eth.getGasPrice()
    const additionalGasWei = web3.value.utils.toWei(additionalGas.value.toString(), 'gwei')
    const finalGasPrice = (BigInt(currentGasPriceBN) + BigInt(additionalGasWei)).toString()

    if (interactionMode.value === 'eth-collection') {
      // ETH归集模式：计算最大可转金额
      const balanceWei = await web3.value.eth.getBalance(wallet.address)
      const gasLimit = 21000

      // 使用finalGasPrice作为实际gas费用
      const maxTotalGas = BigInt(finalGasPrice) * BigInt(gasLimit)
      const amount = BigInt(balanceWei) - maxTotalGas

      // 确保金额不为负数
      value = amount > 0n ? amount.toString() : '0'
    } else {
      // 普通模式：使用用户输入的固定值
      value = transferValue.value === 0 ? 0 : web3.value.utils.toWei(transferValue.value.toString(), 'ether')
    }

    // 获取nonce（使用pending状态获取最新的nonce）
    const nonce = await web3.value.eth.getTransactionCount(wallet.address, 'pending')

    // 获取链ID
    const chainId = await web3.value.eth.getChainId()

    // 构造交易
    const transaction = {
      from: wallet.address,
      to: web3.value.utils.toChecksumAddress(targetAddress.value),
      value: value,
      gas: gas,
      gasPrice: finalGasPrice,
      nonce: nonce,
      chainId: chainId,
      data: contractData.value
    }

    // 签名交易
    const signedTransaction = await web3.value.eth.accounts.signTransaction(transaction, wallet.privateKey)

    // 从rawTransaction计算交易hash
    const txHash = web3.value.utils.keccak256(signedTransaction.rawTransaction)

    // 发送交易并处理结果
    try {
      return await new Promise((resolve) => {
        web3.value.eth.sendSignedTransaction(signedTransaction.rawTransaction)
          .on('transactionHash', function(hash) {
            // 收到交易hash，立即返回成功
            resolve({
              success: true,
              txHash: hash,
              address: wallet.address
            })
          })
          .on('error', function(error) {
            const errorMessage = error.message.toLowerCase()

            // 检查是否是"already known"错误
            if (errorMessage.includes('already known') || errorMessage.includes('known transaction')) {
              console.log(`交易已在内存池中: ${wallet.address}`)
              resolve({
                success: true,
                txHash: txHash,
                address: wallet.address,
                note: '交易已在内存池中'
              })
            } else {
              console.error('交易执行失败:', error.message)
              resolve({
                success: false,
                error: `交易失败: ${getErrorMessage(error)}`,
                txHash: txHash,
                address: wallet.address
              })
            }
          })
          .catch(function(error) {
            const errorMessage = error.message.toLowerCase()

            // 检查是否是"already known"错误
            if (errorMessage.includes('already known') || errorMessage.includes('known transaction')) {
              console.log(`交易已在内存池中: ${wallet.address}`)
              resolve({
                success: true,
                txHash: txHash,
                address: wallet.address,
                note: '交易已在内存池中'
              })
            } else {
              console.error('交易发送失败:', error.message)
              resolve({
                success: false,
                error: `发送失败: ${getErrorMessage(error)}`,
                txHash: txHash,
                address: wallet.address
              })
            }
          })

        // 30秒超时保护
        setTimeout(() => {
          resolve({
            success: false,
            error: '交易发送超时',
            txHash: txHash,
            address: wallet.address
          })
        }, 30000)
      })
    } catch (sendError) {
      const errorMessage = sendError.message.toLowerCase()

      // 检查是否是nonce相关错误，如果是且还有重试次数，则重试
      if ((errorMessage.includes('nonce') || errorMessage.includes('already known')) && retryCount < maxRetries) {
        console.log(`Nonce错误，等待2秒后重试 (${retryCount + 1}/${maxRetries}): ${wallet.address}`)
        await sleep(2)
        return await executeEthTransaction(wallet, retryCount + 1)
      }

      // 检查是否是"already known"错误
      if (errorMessage.includes('already known') || errorMessage.includes('known transaction')) {
        console.log(`交易已在内存池中: ${wallet.address}`)
        return {
          success: true,
          txHash: txHash,
          address: wallet.address,
          note: '交易已在内存池中'
        }
      }

      // 发送交易失败，返回错误信息
      console.error('交易发送失败:', sendError.message)
      return {
        success: false,
        error: `交易发送失败: ${getErrorMessage(sendError)}`,
        txHash: txHash,
        address: wallet.address
      }
    }
  } catch (error) {
    return {
      success: false,
      error: getErrorMessage(error),
      address: wallet.address
    }
  }
}

// 开始执行
async function startInteraction() {
  if (walletData.value.length === 0) {
    alert('请先上传钱包文件')
    return
  }

  // 代币归集模式的额外验证
  if (interactionMode.value === 'token-collection') {
    if (!tokenContractAddress.value) {
      alert('请先输入代币合约地址')
      return
    }

    if (!targetAddress.value) {
      alert('请先输入归集目标地址')
      return
    }

    if (!tokenInfo.value) {
      alert('请先获取代币信息')
      return
    }

    if (!useMaxAmount.value && (!collectionAmount.value || collectionAmount.value <= 0)) {
      alert('请输入有效的归集数量')
      return
    }
  }

  // 确保使用最新的Web3实例
  console.log('开始交互，使用RPC端点:', rpcUrl.value)
  initWeb3(rpcUrl.value)

  if (!web3.value) {
    alert('Web3初始化失败，请检查RPC端点')
    return
  }

  isRunning.value = true
  stopFlag = false
  executedCount.value = 0
  successCount.value = 0
  failureCount.value = 0
  transactionResults.value = []
  currentLoop.value = 0
  currentWallet.value = 0
  
  // 计算总交易数
  totalTransactions.value = calculateTotalTransactions()

  if (interactionMode.value === 'eth-collection') {
    console.log(`开始ETH归集模式执行: ${walletData.value.length}个钱包，每个执行1次，总共${totalTransactions.value}笔交易`)
  } else if (interactionMode.value === 'token-collection') {
    console.log(`开始代币归集模式执行: ${walletData.value.length}个钱包，代币: ${tokenInfo.value.symbol}，总共${totalTransactions.value}笔交易`)
  } else {
    console.log(`开始按轮次循环执行: ${loopCount.value}轮循环，${walletData.value.length}个钱包，总共${totalTransactions.value}笔交易`)
  }

  try {
    // 归集模式下只执行一轮，普通模式按设定轮数执行
    const effectiveLoopCount = isCollectionMode.value ? 1 : loopCount.value

    // 外层循环：按轮次执行
    for (let loop = 1; loop <= effectiveLoopCount && !stopFlag; loop++) {
      currentLoop.value = loop

      if (interactionMode.value === 'eth-collection') {
        console.log(`开始ETH归集执行，将处理所有${walletData.value.length}个钱包`)
      } else if (interactionMode.value === 'token-collection') {
        console.log(`开始代币归集执行，将处理所有${walletData.value.length}个钱包`)
      } else {
        console.log(`开始第${loop}轮执行，将处理所有${walletData.value.length}个钱包`)
      }
      
      // 内层循环：每轮处理所有钱包
      for (let walletIndex = 0; walletIndex < walletData.value.length && !stopFlag; walletIndex++) {
        currentWallet.value = walletIndex + 1
        const wallet = walletData.value[walletIndex]
        
        if (interactionMode.value === 'eth-collection') {
          console.log(`ETH归集模式 - 处理第${currentWallet.value}个钱包: ${wallet.address}`)
        } else if (interactionMode.value === 'token-collection') {
          console.log(`代币归集模式 - 处理第${currentWallet.value}个钱包: ${wallet.address}`)
        } else {
          console.log(`第${loop}轮 - 处理第${currentWallet.value}个钱包: ${wallet.address}`)
        }
        
        // Gas价格检查循环
        let gasCheckPassed = false
        while (!gasCheckPassed && !stopFlag) {
          // 获取当前Gas价格
          const gasPrice = await getCurrentGasPrice()
          currentGasPrice.value = gasPrice

          if (gasPrice < maxGasPrice.value && gasPrice > 0) {
            gasCheckPassed = true
            if (interactionMode.value === 'eth-collection') {
              statusMessage.value = `ETH归集模式 - 钱包${currentWallet.value}/${walletData.value.length}: Gas价格合适 (${parseFloat(gasPrice.toFixed(4))} Gwei)，执行归集...`
            } else if (interactionMode.value === 'token-collection') {
              statusMessage.value = `代币归集模式 - 钱包${currentWallet.value}/${walletData.value.length}: Gas价格合适 (${parseFloat(gasPrice.toFixed(4))} Gwei)，执行归集...`
            } else {
              statusMessage.value = `第${loop}轮 - 钱包${currentWallet.value}/${walletData.value.length}: Gas价格合适 (${parseFloat(gasPrice.toFixed(4))} Gwei)，执行交易...`
            }
            
            const result = await executeTransaction(wallet)
            result.loop = loop
            result.walletIndex = currentWallet.value
            result.totalWallets = walletData.value.length
            transactionResults.value.push(result)
            
            if (result.success) {
              successCount.value++
            } else {
              failureCount.value++
            }
            
            executedCount.value++
            
            // 短暂延迟避免请求过于频繁
            await sleep(1)
            
          } else {
            if (interactionMode.value === 'eth-collection') {
              statusMessage.value = `ETH归集模式 - 钱包${currentWallet.value}/${walletData.value.length}: Gas价格过高 (${parseFloat(gasPrice.toFixed(4))} Gwei)，等待价格下降...`
            } else if (interactionMode.value === 'token-collection') {
              statusMessage.value = `代币归集模式 - 钱包${currentWallet.value}/${walletData.value.length}: Gas价格过高 (${parseFloat(gasPrice.toFixed(4))} Gwei)，等待价格下降...`
            } else {
              statusMessage.value = `第${loop}轮 - 钱包${currentWallet.value}/${walletData.value.length}: Gas价格过高 (${parseFloat(gasPrice.toFixed(4))} Gwei)，等待价格下降...`
            }
            await sleep(1)
          }
        }
        
        // 每个钱包执行完成后的短暂延迟
        if (walletIndex < walletData.value.length - 1) {
          if (interactionMode.value === 'eth-collection') {
            statusMessage.value = `ETH归集模式 - 钱包${currentWallet.value}完成，准备下一个钱包...`
          } else if (interactionMode.value === 'token-collection') {
            statusMessage.value = `代币归集模式 - 钱包${currentWallet.value}完成，准备下一个钱包...`
          } else {
            statusMessage.value = `第${loop}轮 - 钱包${currentWallet.value}完成，准备下一个钱包...`
          }
          await sleep(1)
        }
      }
      
      // 每轮完成后的延迟
      if (loop < effectiveLoopCount) {
        if (isCollectionMode.value) {
          // 归集模式不会有多轮，这里不会执行到
        } else {
          statusMessage.value = `第${loop}轮完成！所有${walletData.value.length}个钱包已执行，准备下一轮...`
          await sleep(3)
        }
      }
    }

    if (interactionMode.value === 'eth-collection') {
      statusMessage.value = `ETH归集完成！${walletData.value.length}个钱包，总共完成${executedCount.value}笔归集交易`
    } else if (interactionMode.value === 'token-collection') {
      statusMessage.value = `代币归集完成！${walletData.value.length}个钱包，总共完成${executedCount.value}笔归集交易`
    } else {
      statusMessage.value = `所有执行完成！${loopCount.value}轮循环，${walletData.value.length}个钱包，总共完成${executedCount.value}笔交易`
    }
  } catch (error) {
    console.error('执行过程中出错:', error)
    statusMessage.value = '执行过程中出错: ' + getErrorMessage(error)
  } finally {
    isRunning.value = false
  }
}

// 停止执行
function stopInteraction() {
  stopFlag = true
  statusMessage.value = '正在停止执行...'
}

// 监听来自其他页面的网络配置变化
function loadSharedNetworkConfig() {
  const currentData = JSON.parse(localStorage.getItem(STORAGE_KEYS.CONTRACT_INTERACTION) || '{}')
  if (currentData.selectedNetwork && currentData.rpcUrl) {
    // 检查是否有变化，避免无限循环
    if (selectedNetwork.value !== currentData.selectedNetwork || rpcUrl.value !== currentData.rpcUrl) {
      console.log('检测到其他页面的网络配置变化，正在同步...')
      selectedNetwork.value = currentData.selectedNetwork
      rpcUrl.value = currentData.rpcUrl
      
      // 重新初始化Web3并刷新数据
      initWeb3(rpcUrl.value)
      setTimeout(async () => {
        const gasPrice = await getCurrentGasPrice()
        if (gasPrice > 0) {
          currentGasPrice.value = gasPrice
          console.log('同步后gas价格已刷新:', gasPrice, 'Gwei')
        }
        
        // 如果已经加载了钱包，刷新它们的余额
        if (walletData.value.length > 0) {
          console.log('同步后刷新已加载钱包的余额...')
          const updatedWallets = await loadWalletBalances(walletData.value)
          walletData.value = updatedWallets
          console.log('钱包余额刷新完成')
        }
      }, 500)
    }
  }
}

// 页面可见性变化时检查网络配置是否有更新
document.addEventListener('visibilitychange', () => {
  if (!document.hidden) {
    loadSharedNetworkConfig()
  }
})

// 监听localStorage变化（同一浏览器窗口内的实时同步）
window.addEventListener('storage', (e) => {
  if (e.key === STORAGE_KEYS.CONTRACT_INTERACTION) {
    loadSharedNetworkConfig()
  }
})

// 初始化Gas价格设置
async function initializeGasPrice() {
  try {
    // 确保Web3已初始化
    if (!web3.value) {
      initWeb3(rpcUrl.value)
    }
    
    // 获取实时Gas价格
    const gasPrice = await getCurrentGasPrice()
    if (gasPrice > 0) {
      const rounded = Number(gasPrice.toFixed(4))
      currentGasPrice.value = rounded
      if (maxGasPrice.value <= 0) {
        maxGasPrice.value = rounded
        console.log('初始化最大Gas价格为实时价格:', rounded, 'Gwei')
      }
    }
  } catch (error) {
    console.warn('初始化Gas价格失败:', error)
    // 如果获取失败，仅在未设置时使用默认值
    if (maxGasPrice.value <= 0) {
      maxGasPrice.value = 1.3
    }
  }
}

// 初始加载时获取最新的网络配置和Gas价格
loadSharedNetworkConfig()
initializeGasPrice()
</script>

<style scoped>
/* 模式选择器样式 */
.mode-selector {
  display: flex;
  gap: 20px;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.mode-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(107, 155, 209, 0.3);
  background: rgba(107, 155, 209, 0.1);
  transition: all 0.3s ease;
}

.mode-option:hover {
  background: rgba(107, 155, 209, 0.2);
  border-color: rgba(107, 155, 209, 0.5);
}

.mode-option:has(.mode-radio:checked) {
  background: rgba(107, 155, 209, 0.3);
  border-color: #6b9bd1;
}

.mode-radio {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #6b9bd1;
}

.mode-text {
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  user-select: none;
}

/* 代币信息样式 */
.token-info {
  margin-top: 1rem;
  padding: 0.5rem;
  background: rgba(107, 155, 209, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(107, 155, 209, 0.3);
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.info-row.compact {
  margin-bottom: 0;
  padding: 0.25rem 0;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 500;
  color: #cccccc;
}

.value {
  font-weight: 600;
  color: #ffffff;
}

/* 归集模式样式 */
.collection-mode {
  display: flex;
  align-items: center;
  gap: 12px;
  white-space: nowrap;
}

.collection-tip {
  color: #d68910;
  font-style: italic;
  font-size: 13px;
  margin-top: 12px;
  padding: 10px 15px;
  background: rgba(214, 137, 16, 0.1);
  border: 1px solid rgba(214, 137, 16, 0.3);
  border-radius: 6px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #ffffff;
  flex-wrap: wrap;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #6b9bd1;
  flex-shrink: 0;
}

.checkbox-text {
  user-select: none;
  font-size: 14px;
  flex-shrink: 0;
}

.collection-tip {
  color: #d68910;
  font-style: italic;
  font-size: 12px;
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(214, 137, 16, 0.1);
  border: 1px solid rgba(214, 137, 16, 0.3);
  border-radius: 6px;
}

.collection-tip-inline {
  color: #d68910;
  font-style: italic;
  font-size: 11px;
  line-height: 1.3;
  padding: 3px 6px;
  background: rgba(214, 137, 16, 0.1);
  border: 1px solid rgba(214, 137, 16, 0.3);
  border-radius: 3px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 循环模式信息样式 */
.loop-mode-info {
  margin-top: 12px;
  padding: 10px 15px;
  background: rgba(107, 155, 209, 0.1);
  border: 1px solid rgba(107, 155, 209, 0.3);
  border-radius: 6px;
}

.info-text {
  color: #6b9bd1;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.4;
}

/* 执行状态网格：三行两列布局 */
.execution-status-grid {
  display: grid !important;
  grid-template-columns: 1fr 1fr !important;
  grid-template-rows: 1fr 1fr 1fr !important;
  gap: 15px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mode-selector {
    flex-direction: column;
    gap: 10px;
  }

  .mode-option {
    padding: 10px 15px;
  }

  .collection-mode {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    white-space: normal;
    margin-top: 8px;
  }

  .collection-tip {
    margin-left: 0;
    font-size: 12px;
    padding: 8px 12px;
  }

  .collection-tip-inline {
    margin-top: 5px;
    font-size: 11px;
  }

  .token-info {
    margin-top: 8px;
    padding: 6px 10px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-row.compact {
    padding: 0.2rem 0;
  }

  /* 移动端执行状态网格改为单列 */
  .execution-status-grid {
    grid-template-columns: 1fr !important;
    grid-template-rows: repeat(6, 1fr) !important;
  }

  .loop-mode-info {
    margin-top: 8px;
    padding: 8px 12px;
  }

  .info-text {
    font-size: 12px;
  }
}
</style> 